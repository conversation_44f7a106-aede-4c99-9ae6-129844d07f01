/**
 * Test script for the Microchip API provider
 * 
 * This script tests the Microchip API provider by creating a new API configuration
 * and sending a test message to verify functionality.
 */

import * as vscode from 'vscode'
import { ProviderSettings } from './shared/api'
import { buildApiHandler } from './api'
import { logger } from './utils/logging'

/**
 * Tests the Microchip API provider by creating a new configuration and sending a test message
 * 
 * @param apiKey The Microchip API key to use for testing
 * @param baseUrl Optional custom base URL for the Microchip API
 * @returns A promise that resolves to a success message or rejects with an error
 */
export async function testMicrochipProvider(
    apiKey: string,
    baseUrl?: string
): Promise<string> {
    try {
        logger.info('Starting Microchip provider test', {
            ctx: 'microchip-test',
            hasApiKey: !!apiKey,
            hasCustomBaseUrl: !!baseUrl
        })

        // Create a provider configuration for testing
        const providerSettings: ProviderSettings = {
            apiProvider: 'microchip',
            microchipApiKey: api<PERSON><PERSON>,
            ...(baseUrl ? { microchipBaseUrl: baseUrl } : {})
        }

        // Create an API handler with the test configuration
        const apiHandler = buildApiHandler(providerSettings)

        // Get model information
        const { id: modelId, info: modelInfo } = apiHandler.getModel()
        logger.info('Using Microchip model', {
            ctx: 'microchip-test',
            modelId,
            contextWindow: modelInfo.contextWindow
        })

        // Test system message
        const systemPrompt = 'You are a helpful assistant for embedded systems development.'
        
        // Test user message
        const testMessage = 'What is the difference between a microcontroller and a microprocessor?'
        
        // Create a progress notification
        await vscode.window.withProgress(
            {
                location: vscode.ProgressLocation.Notification,
                title: 'Testing Microchip API Provider',
                cancellable: false
            },
            async (progress) => {
                progress.report({ message: 'Sending test message...' })

                // Send a test message to the API
                const messageStream = apiHandler.createMessage(
                    systemPrompt,
                    [{ role: 'user', content: testMessage }]
                )

                // Process the response
                let responseText = ''
                let tokenStats = { input: 0, output: 0 }

                for await (const chunk of messageStream) {
                    if (chunk.type === 'text' && chunk.text) {
                        responseText += chunk.text
                    } else if (chunk.type === 'usage') {
                        tokenStats.input = chunk.inputTokens || 0
                        tokenStats.output = chunk.outputTokens || 0
                    }
                }

                // Validate the response
                if (!responseText) {
                    throw new Error('No response received from the Microchip API')
                }

                progress.report({ message: 'Test completed successfully!' })

                logger.info('Microchip API test completed successfully', {
                    ctx: 'microchip-test',
                    responseLength: responseText.length,
                    inputTokens: tokenStats.input,
                    outputTokens: tokenStats.output
                })

                // Show the response in an information message
                await vscode.window.showInformationMessage(
                    'Microchip API test successful!',
                    { modal: false, detail: `Response (${responseText.length} chars):\n\n${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}` }
                )
            }
        )

        return 'Microchip API provider test completed successfully'
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        logger.error('Microchip API test failed', {
            ctx: 'microchip-test',
            error: errorMessage
        })

        // Show error message
        await vscode.window.showErrorMessage(
            'Microchip API test failed',
            { modal: true, detail: errorMessage }
        )

        throw new Error(`Microchip API test failed: ${errorMessage}`)
    }
}

/**
 * Command handler to test the Microchip provider with user input
 */
export async function testMicrochipProviderCommand() {
    try {
        // Ask for API key
        const apiKey = await vscode.window.showInputBox({
            prompt: 'Enter your Microchip API key',
            placeHolder: 'API Key',
            password: true,
            ignoreFocusOut: true
        })

        if (!apiKey) {
            throw new Error('API key is required')
        }

        // Ask for custom base URL (optional)
        const useCustomUrl = await vscode.window.showQuickPick(
            ['Yes', 'No'],
            {
                placeHolder: 'Use custom API URL?',
                ignoreFocusOut: true
            }
        )

        let baseUrl: string | undefined
        if (useCustomUrl === 'Yes') {
            baseUrl = await vscode.window.showInputBox({
                prompt: 'Enter custom Microchip API URL',
                placeHolder: 'https://ai-apps.microchip.com/CodeGPTAPI/api/Chat/CodeCompletionLLM',
                ignoreFocusOut: true
            })
        }

        // Run the test
        await testMicrochipProvider(apiKey, baseUrl)
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        logger.error('Microchip provider test command failed', {
            ctx: 'microchip-test',
            error: errorMessage
        })
        vscode.window.showErrorMessage(`Microchip provider test failed: ${errorMessage}`)
    }
}
