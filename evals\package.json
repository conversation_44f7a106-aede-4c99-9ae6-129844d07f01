{"name": "@evals/monorepo", "private": true, "packageManager": "pnpm@10.7.1+sha512.2d92c86b7928dc8284f53494fb4201f983da65f0fb4f0d40baafa5cf628fa31dae3e5968f12466f17df7e97310e30f343a648baea1b9b350685dafafffdf5808", "scripts": {"lint": "turbo lint --log-order grouped --output-logs new-only", "check-types": "turbo check-types --log-order grouped --output-logs new-only", "test": "turbo test --log-order grouped --output-logs new-only", "format": "turbo format --log-order grouped --output-logs new-only", "build": "turbo build --log-order grouped --output-logs new-only", "web": "turbo dev --filter @evals/web", "cli": "turbo dev --filter @evals/cli -- run", "drizzle:studio": "pnpm --filter @evals/db db:studio"}, "devDependencies": {"@dotenvx/dotenvx": "^1.41.0", "@eslint/js": "^9.25.1", "eslint": "^9.25.1", "globals": "^16.0.0", "prettier": "^3.5.3", "tsx": "^4.19.4", "turbo": "^2.5.2", "typescript": "5.8.3", "typescript-eslint": "^8.31.1"}}