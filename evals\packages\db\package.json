{"name": "@evals/db", "private": true, "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src/**/*.ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest --globals --run", "format": "prettier --write src", "drizzle-kit": "dotenvx run -f ../../.env -- tsx node_modules/drizzle-kit/bin.cjs", "db:generate": "pnpm drizzle-kit generate", "db:migrate": "pnpm drizzle-kit migrate", "db:push": "pnpm drizzle-kit push", "db:pull": "pnpm drizzle-kit pull", "db:check": "pnpm drizzle-kit check", "db:up": "pnpm drizzle-kit up", "db:studio": "pnpm drizzle-kit studio", "db:enable-wal": "dotenvx run -f ../../.env -- tsx scripts/enable-wal.mts", "db:copy-run": "dotenvx run -f ../../.env -- tsx scripts/copy-run.mts"}, "dependencies": {"@evals/types": "workspace:^", "@libsql/client": "^0.15.0", "drizzle-orm": "^0.40.0", "drizzle-zod": "^0.7.0", "p-map": "^7.0.3", "zod": "^3.24.2"}, "devDependencies": {"@evals/eslint-config": "workspace:^", "@evals/typescript-config": "workspace:^", "drizzle-kit": "^0.31.0", "execa": "^9.5.2", "vitest": "^3.0.9"}}