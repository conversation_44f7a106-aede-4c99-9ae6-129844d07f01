/**
 * Microchip API Provider for Roo-Code with Agent Mode Support
 */

import { Anthropic } from "@anthropic-ai/sdk";
import NodeCache from "node-cache";

import {
  ApiHandlerOptions,
  MicrochipModelId,
  microchipDefaultModelId,
  microchipModels,
} from "../../shared/api";
import { ApiStream } from "../transform/stream";
import { logger } from "../../utils/logging";
import { SingleCompletionHandler } from "../index";
import { BaseProvider } from "./base-provider";

const CACHE_TTL = 30;
const RESPONSE_CACHE_SIZE = 100;

export class MicrochipHandler
  extends BaseProvider
  implements SingleCompletionHandler
{
  protected options: ApiHandlerOptions;
  private responseCache: NodeCache;
  private tokenCountCache: NodeCache;
  private apiUrl: string;

  constructor(options: ApiHandlerOptions) {
    super();
    this.options = options;
    this.responseCache = new NodeCache({
      stdTTL: CACHE_TTL * 60,
      checkperiod: 60,
      maxKeys: RESPONSE_CACHE_SIZE,
    });
    this.tokenCountCache = new NodeCache({
      stdTTL: CACHE_TTL * 60,
      checkperiod: 60,
    });
    this.apiUrl =
      this.options.microchipBaseUrl ??
      "https://ai-apps.microchip.com/CodeGPTAPI/api/Chat/CodeCompletionLLM";
    logger.info("Initializing Microchip API client", {
      ctx: "microchip",
      apiUrl: this.apiUrl,
    });
  }

  async completePrompt(prompt: string): Promise<string> {
    const { id: modelId, info } = this.getModel();
    const cacheKey = this.generateCacheKey("", [{ role: "user", content: prompt }], modelId);
    const useCache = info.supportsPromptCache && !this.options.promptCachingDisabled;

    if (useCache && this.responseCache.has(cacheKey)) {
      const cached = this.responseCache.get<string>(cacheKey);
      if (cached) return cached;
    }

    const processedPrompt = this.injectAgentPrompt(
      this.ensurePromptStartsWithImStart(prompt)
    );
    const payload = { questions: [processedPrompt] };

    const response = await fetch(this.apiUrl, {
      method: "POST",
      headers: this.getHeaders(),
      body: JSON.stringify(payload),
    });

    const result = await response.text();
    if (useCache && result) this.responseCache.set(cacheKey, result);
    return result;
  }

  override async *createMessage(
    systemPrompt: string,
    messages: Anthropic.Messages.MessageParam[],
    cacheKey?: string
  ): ApiStream {
    const { id: model, info } = this.getModel();
    const cacheKeyFinal =
      cacheKey || this.generateCacheKey(systemPrompt, messages, model);
    const useCache = info.supportsPromptCache && !this.options.promptCachingDisabled;

    let processedMessages = this.injectAgentPrompt(
      this.processMessages(systemPrompt, messages)
    );

    if (useCache && this.responseCache.has(cacheKeyFinal)) {
      const cached = this.responseCache.get<string>(cacheKeyFinal);
      if (cached) {
        yield { type: "text", text: cached };
        yield {
          type: "usage",
          inputTokens: await this.estimateInputTokens(systemPrompt, messages),
          outputTokens: await this.estimateOutputTokens(cached),
        };
        return;
      }
    }

    const response = await fetch(this.apiUrl, {
      method: "POST",
      headers: this.getHeaders(),
      body: JSON.stringify({ questions: [processedMessages] }),
    });

    const reader = response.body?.getReader();
    if (!reader) throw new Error("Microchip API stream error");

    let result = "";
    while (true) {
      const { value, done } = await reader.read();
      if (value) {
        const chunk = new TextDecoder().decode(value);
        result += chunk;
        yield { type: "text", text: chunk };
      }
      if (done) break;
    }

    // 🔧 Agent parsing logic
    const match = result.match(/Action:\s*(\w+)\s*Action Input:\s*(\{.*\})/);
    if (match) {
      const toolName = match[1];
      const args = JSON.parse(match[2]);
      const toolOutput = await this.callTool(toolName, args);

      const followUpPrompt = this.ensurePromptStartsWithImStart(
        `Tool result: ${toolOutput}\nFinal Answer:`
      );

      const finalRes = await fetch(this.apiUrl, {
        method: "POST",
        headers: this.getHeaders(),
        body: JSON.stringify({ questions: [followUpPrompt] }),
      });

      const finalText = await finalRes.text();
      yield { type: "text", text: finalText };
    }

    if (useCache) this.responseCache.set(cacheKeyFinal, result);
    yield {
      type: "usage",
      inputTokens: await this.estimateInputTokens(systemPrompt, messages),
      outputTokens: await this.estimateOutputTokens(result),
    };
  }

  private injectAgentPrompt(content: string): string {
    return `<|im_start|>user\nYou are an AI assistant. Think step by step.\nUse tools when needed with this format:\nThought: why tool is needed\nAction: tool_name\nAction Input: {\"key\":\"value\"}\n\n${content}`;
  }

  private processMessages(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): string {
    const last = messages[messages.length - 1];
    let content = "";
    if (typeof last?.content === "string") content = this.stripImages(last.content);
    else if (Array.isArray(last?.content)) {
      content = last.content
        .filter((b) => b.type === "text")
        .map((b) => this.stripImages(b.text))
        .join("\n");
    }
    return this.ensurePromptStartsWithImStart(`${systemPrompt}\n\n${content}`);
  }

  private getHeaders(): Record<string, string> {
    return {
      "Content-Type": "application/json",
      "api-key": this.options.microchipApiKey ?? "",
    };
  }

  private stripImages(content: string): string {
    return content; // Add real implementation if needed
  }

  private ensurePromptStartsWithImStart(prompt: string): string {
    return prompt.startsWith("<|im_start|>") ? prompt : `<|im_start|>user\n${prompt}`;
  }

  async callTool(tool: string, args: any): Promise<string> {
    switch (tool) {
      case "searchDocs":
        return `Found relevant info for query: ${args.query}`;
      case "lookupPart":
        return `Part ${args.id} is in stock.`;
      default:
        return `Tool '${tool}' not recognized.`;
    }
  }

  override getModel() {
    const modelId = "microchip-chatbot-internal";
    const info =
      microchipModels[modelId as MicrochipModelId] ||
      microchipModels[microchipDefaultModelId];
    return { id: modelId, info };
  }

  override async countTokens(
    content: Array<Anthropic.Messages.ContentBlockParam>
  ): Promise<number> {
    if (!content.length) return 0;
    const str = JSON.stringify(content);
    const key = `token_${Buffer.from(str).toString("base64").substring(0, 40)}`;
    if (this.tokenCountCache.has(key)) return this.tokenCountCache.get<number>(key) || 0;
    const count = await super.countTokens(content);
    this.tokenCountCache.set(key, count);
    return count;
  }

  private generateCacheKey(
    systemPrompt: string,
    messages: Anthropic.Messages.MessageParam[],
    modelId: string
  ): string {
    const simplified = messages.map((m) => ({
      role: m.role,
      content:
        typeof m.content === "string"
          ? m.content
          : JSON.stringify(m.content),
    }));
    const keyData = JSON.stringify({ system: systemPrompt, messages: simplified, model: modelId });
    return `microchip_${Buffer.from(keyData).toString("base64").substring(0, 64)}`;
  }

  private async estimateInputTokens(
    systemPrompt: string,
    messages: Anthropic.Messages.MessageParam[]
  ): Promise<number> {
    const blocks: Anthropic.Messages.ContentBlockParam[] = [];
    if (systemPrompt) blocks.push({ type: "text", text: systemPrompt });
    for (const m of messages) {
      if (typeof m.content === "string") blocks.push({ type: "text", text: m.content });
      else if (Array.isArray(m.content)) blocks.push(...m.content);
    }
    return this.countTokens(blocks);
  }

  private async estimateOutputTokens(text: string): Promise<number> {
    return this.countTokens([{ type: "text", text }]);
  }
}
