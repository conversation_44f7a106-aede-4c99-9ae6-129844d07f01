import { useCallback } from "react"
import { VSCodeTextField } from "@vscode/webview-ui-toolkit/react"

import { ProviderSettings, microchipDefaultModelId, microchipModels, MicrochipModelId } from "@roo/shared/api"

import { VSCodeButtonLink } from "@src/components/common/VSCodeButtonLink"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@src/components/ui"

import { inputEventTransform } from "../transforms"
import { useAppTranslation } from "@src/i18n/TranslationContext"
import { ModelInfoView } from "../ModelInfoView"

type MicrochipProps = {
	apiConfiguration: ProviderSettings
	setApiConfigurationField: (field: keyof ProviderSettings, value: ProviderSettings[keyof ProviderSettings]) => void
}

export const Microchip = ({ apiConfiguration, setApiConfigurationField }: MicrochipProps) => {
	const { t } = useAppTranslation()

	const handleInputChange = useCallback(
		<K extends keyof ProviderSettings, E>(
			field: K,
			transform = inputEventTransform as (event: E) => ProviderSettings[K],
		) =>
			(event: E | Event) => {
				setApiConfigurationField(field, transform(event as E))
			},
		[setApiConfigurationField],
	)

	// Get the selected model ID or use the default
	const selectedModelId = (apiConfiguration?.apiModelId as MicrochipModelId) || microchipDefaultModelId

	// Get the model info for the selected model
	const selectedModelInfo = microchipModels[selectedModelId] || microchipModels[microchipDefaultModelId]

	// Create options for the model dropdown
	const modelOptions = Object.entries(microchipModels).map(([id, info]) => ({
		value: id,
		label: id,
		description: info.description,
	}))

	return (
		<>
			<div className="flex flex-col gap-3">
				<VSCodeTextField
					value={apiConfiguration?.microchipBaseUrl || ""}
					type="url"
					onInput={handleInputChange("microchipBaseUrl")}
					placeholder="https://ai-apps.microchip.com/CodeGPTAPI/api/Chat/CodeCompletionLLM"
					className="w-full">
					<label className="block font-medium mb-1">Microchip API URL</label>
				</VSCodeTextField>
				<VSCodeTextField
					value={apiConfiguration?.microchipApiKey || ""}
					type="password"
					onInput={handleInputChange("microchipApiKey")}
					placeholder="Enter your Microchip API key"
					className="w-full">
					<label className="block font-medium mb-1">Microchip API Key</label>
				</VSCodeTextField>

				<div>
					<label className="block font-medium mb-1">{t("settings:providers.model")}</label>
					<div className="flex flex-col gap-2">
						<Select
							value={selectedModelId}
							onValueChange={(value) => setApiConfigurationField("apiModelId", value)}
						>
							<SelectTrigger className="w-full">
								<SelectValue placeholder={t("settings:common.select")} />
							</SelectTrigger>
							<SelectContent>
								{modelOptions.map((option) => (
									<SelectItem key={option.value} value={option.value}>
										{option.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<VSCodeTextField
							value={apiConfiguration?.apiModelId || ""}
							onInput={handleInputChange("apiModelId")}
							placeholder="Or enter custom model ID (e.g., microchip-api)"
							className="w-full">
							<label className="block font-medium mb-1">Custom Model ID</label>
						</VSCodeTextField>
						<div className="text-xs text-vscode-descriptionForeground">
							You can select a predefined model or enter a custom model ID
						</div>
					</div>
				</div>

				{selectedModelId && selectedModelInfo && (
					<ModelInfoView
						apiProvider="microchip"
						selectedModelId={selectedModelId}
						modelInfo={selectedModelInfo}
						isDescriptionExpanded={true}
						setIsDescriptionExpanded={() => {}}
					/>
				)}

				<div className="text-sm text-vscode-descriptionForeground">
					You can obtain a Microchip API key from{" "}
					<VSCodeButtonLink href="https://www.microchip.com/ai-assistant/accesskey/">
						microchip.com/ai-assistant/accesskey
					</VSCodeButtonLink>
				</div>
			</div>
		</>
	)
}
